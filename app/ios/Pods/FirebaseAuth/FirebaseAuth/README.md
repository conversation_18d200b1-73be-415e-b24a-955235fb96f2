# Firebase Auth for iOS

Firebase Auth enables apps to easily support multiple authentication options
for their end users.

Please visit [our developer site](https://firebase.google.com/docs/auth/) for
integration instructions, documentation, support information, and terms of
service.

# Firebase Auth Development

Example/Auth contains a set of samples and tests that integrate with
FirebaseAuth.

The unit tests run without any additional configuration along with the rest of
Firebase. See [Tests/SampleSwift/README.md](Tests/SampleSwift/README.md) for
information about setting up, running, and testing the samples.
