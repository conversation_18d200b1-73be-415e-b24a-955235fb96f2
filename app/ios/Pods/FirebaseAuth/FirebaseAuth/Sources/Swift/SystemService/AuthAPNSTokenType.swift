// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#if !os(macOS)
  import Foundation

  /// The APNs token type for the app.
  ///
  /// This enum is available on iOS, macOS Catalyst, tvOS, and watchOS only.

  @objc(FIRAuthAPNSTokenType) public enum AuthAPNSTokenType: Int {
    /// Unknown token type.
    ///
    /// The actual token type will be detected from the provisioning profile in the app's bundle.
    case unknown

    /// Sandbox token type.
    case sandbox

    /// Production token type.
    case prod
  }
#endif
