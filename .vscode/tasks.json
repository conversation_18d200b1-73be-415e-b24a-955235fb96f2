{"version": "2.0.0", "tasks": [{"label": "Flutter: <PERSON> Dev", "type": "shell", "command": "flutter", "args": ["run", "--flavor", "dev"], "options": {"cwd": "${workspaceFolder}/app"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "Flutter: Run Prod", "type": "shell", "command": "flutter", "args": ["run", "--flavor", "prod"], "options": {"cwd": "${workspaceFolder}/app"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "Flutter: Build APK Dev", "type": "shell", "command": "flutter", "args": ["build", "apk", "--flavor", "dev"], "options": {"cwd": "${workspaceFolder}/app"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "Flutter: Build APK Prod", "type": "shell", "command": "flutter", "args": ["build", "apk", "--flavor", "prod"], "options": {"cwd": "${workspaceFolder}/app"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "Flutter: Build iOS Dev", "type": "shell", "command": "flutter", "args": ["build", "ios", "--flavor", "dev"], "options": {"cwd": "${workspaceFolder}/app"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "Flutter: Build iOS Prod", "type": "shell", "command": "flutter", "args": ["build", "ios", "--flavor", "prod"], "options": {"cwd": "${workspaceFolder}/app"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}]}