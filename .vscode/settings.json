{"dart.flutterSdkPath": null, "dart.debugExternalPackageLibraries": false, "dart.debugSdkLibraries": false, "dart.previewFlutterUiGuides": true, "dart.previewFlutterUiGuidesCustomTracking": true, "dart.hotReloadOnSave": "always", "dart.flutterHotReloadOnSave": "always", "dart.flutterCreateAndroidLanguage": "kotlin", "dart.flutterCreateIOSLanguage": "swift", "dart.flutterCreatePlatforms": ["android", "ios"], "files.associations": {"*.dart": "dart"}, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll": "explicit"}, "dart.lineLength": 120, "dart.insertArgumentPlaceholders": false, "dart.showTodos": true, "dart.analysisExcludedFolders": ["build", ".dart_tool"], "files.exclude": {"**/.dart_tool": true, "**/build": true}, "search.exclude": {"**/.dart_tool": true, "**/build": true, "**/*.lock": true}}