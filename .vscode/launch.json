{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "app (dev)",
            "cwd": "app",
            "request": "launch",
            "type": "dart",
            "args": [
                "--flavor",
                "dev"
            ]
        },
        {
            "name": "app (prod)",
            "cwd": "app",
            "request": "launch",
            "type": "dart",
            "args": [
                "--flavor",
                "prod"
            ]
        },
        {
            "name": "app (dev profile mode)",
            "cwd": "app",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile",
            "args": [
                "--flavor",
                "dev"
            ]
        },
        {
            "name": "app (prod profile mode)",
            "cwd": "app",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile",
            "args": [
                "--flavor",
                "prod"
            ]
        },
        {
            "name": "app (dev release mode)",
            "cwd": "app",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release",
            "args": [
                "--flavor",
                "dev"
            ]
        },
        {
            "name": "app (prod release mode)",
            "cwd": "app",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release",
            "args": [
                "--flavor",
                "prod"
            ]
        }
    ]
}